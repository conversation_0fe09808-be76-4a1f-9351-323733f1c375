import React, { useEffect, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { createSlug } from "../../../utils/slugUtils";

// Import images from public folder using direct paths
const image_10 = "/assets/Blog/Image_10.svg";
const image_11 = "/assets/Blog/Image_11.svg";
const image_12 = "/assets/Blog/Image_12.svg";
const image_13 = "/assets/Blog/Image_13.svg";
const image_14 = "/assets/Blog/Image_14.svg";
const image_15 = "/assets/Blog/Image_15.svg";
const image_16 = "/assets/Blog/Image_16.svg";
const image_17 = "/assets/Blog/Image_17.svg";
const PaginationBorder = "/assets/Blog/PaginationBorder.svg";

const blogimages = [
  image_10,
  image_11,
  image_12,
  image_13,
  image_14,
  image_15,
  image_16,
  image_17,
];

// Skeleton Loader Component
const BlogSkeleton = () => (
  <div className="text-white overflow-hidden animate-pulse">
    <div className="w-full h-[200px] sm:h-[303px] bg-gray-200 rounded-[20px] mb-4"></div>
    <div className="p-4">
      <div className="h-4 bg-gray-200 rounded mb-2"></div>
      <div className="h-3 bg-gray-200 rounded mb-2"></div>
      <div className="h-3 bg-gray-200 rounded mb-2 w-3/4"></div>
      <div className="flex items-center mt-2">
        <div className="h-3 bg-gray-200 rounded w-16 mr-2"></div>
        <div className="h-3 bg-gray-200 rounded w-1 mx-2"></div>
        <div className="h-3 bg-gray-200 rounded w-12 ml-2"></div>
      </div>
    </div>
  </div>
);

// Skeleton Grid Component
const SkeletonGrid = () => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
    {Array.from({ length: 8 }).map((_, index) => (
      <BlogSkeleton key={index} />
    ))}
  </div>
);

function ExploreMore() {
  const navigate = useNavigate();
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const blogsPerPage = 10;
  const exploreSectionRef = useRef(null);

  useEffect(() => {
    // Always clear the cached blogs on mount (hard refresh)
    localStorage.removeItem("flowkar_explore_blogs");
    const fetchBlogs = async () => {
      setLoading(true);
      setError(null);
      try {
        // Check localStorage first
        const cachedBlogs = localStorage.getItem("flowkar_explore_blogs");
        if (cachedBlogs) {
          setBlogPosts(JSON.parse(cachedBlogs));
          setLoading(false);
          return;
        }
        const response = await fetch(
          "https://flexioninfotech.com/api/get-all-blogs/"
        );
        if (!response.ok) throw new Error("Network response was not ok");
        const data = await response.json();
        // Map API data to ExploreMore format
        const mappedBlogs = (data?.results?.data || []).map((item) => ({
          id: item.id,
          image: item.video_thumbnail
            ? item.video_thumbnail.startsWith("http")
              ? item.video_thumbnail
              : `https://flexioninfotech.com${
                  item.video_thumbnail.startsWith("/")
                    ? item.video_thumbnail
                    : "/" + item.video_thumbnail
                }`
            : item.banner
            ? item.banner.startsWith("http")
              ? item.banner
              : `https://flexioninfotech.com${
                  item.banner.startsWith("/") ? item.banner : "/" + item.banner
                }`
            : image_10, // fallback to image_10 if no video_thumbnail or banner
          title: item.title || "Untitled Blog",
          description: item.keywords || "",
          category: item.category || "General",
          readTime: item.read_time || "",
          author: item.keywords || "Unknown Author",
          excerpt: item.keywords || "",
          content: item.keywords || "",
          tags: item.keywords ? item.keywords.split(",") : [],
        }));
        setBlogPosts(mappedBlogs);
        // Store in localStorage
        localStorage.setItem(
          "flowkar_explore_blogs",
          JSON.stringify(mappedBlogs)
        );
      } catch (err) {
        setError("Failed to fetch blogs.");
      } finally {
        setLoading(false);
      }
    };
    fetchBlogs();
  }, []);

  const handleBlogClick = (blog) => {
    const blogSlug = createSlug(blog.title);
    navigate(`/blog/${blogSlug}`);
  };

  // Pagination logic
  const indexOfLastBlog = currentPage * blogsPerPage;
  const indexOfFirstBlog = indexOfLastBlog - blogsPerPage;
  const currentBlogs = blogPosts.slice(indexOfFirstBlog, indexOfLastBlog);
  const totalPages = Math.ceil(blogPosts.length / blogsPerPage);

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setLoading(true);
    setCurrentPage(page);
    // Simulate loading delay for UX
    setTimeout(() => {
      setLoading(false);
    }, 400); // 400ms delay
    // Smooth scroll to Explore More section
    if (exploreSectionRef.current) {
      exploreSectionRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    // Show up to 3 pages before and after current page, always show first and last
    for (let i = 1; i <= totalPages; i++) {
      if (
        i === 1 ||
        i === totalPages ||
        (i >= currentPage - 2 && i <= currentPage + 2)
      ) {
        pageNumbers.push(i);
      } else if (
        (i === currentPage - 3 && currentPage - 3 > 1) ||
        (i === currentPage + 3 && currentPage + 3 < totalPages)
      ) {
        pageNumbers.push("...");
      }
    }
    // Remove consecutive duplicates of '...'
    return pageNumbers.filter(
      (num, idx, arr) => num !== "..." || arr[idx - 1] !== "..."
    );
  };

  return (
    <section
      ref={exploreSectionRef}
      className="bg-[#f9f8f2] text-white py-12 px-4 sm:px-6 md:px-12 lg:px-20 rounded-[32px] w-[90%] mx-auto mb-[60px]"
    >
      {/* Header */}
      <div className="flex flex-col items-center text-center mb-10">
        <h2 className="text-black text-xl sm:text-2xl md:text-3xl font-semibold">
          Explore More From Our Blog
        </h2>
        {/* Optional: <button className="text-sm font-medium underline mt-2">See All</button> */}
      </div>

      {/* Blog Grid */}
      {loading ? (
        <SkeletonGrid />
      ) : error ? (
        <div className="col-span-full text-center text-red-500 py-8">
          {error}
        </div>
      ) : blogPosts.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
          {currentBlogs.map((post, index) => (
            <div
              key={index}
              className="text-white overflow-hidden cursor-pointer  transition-transform duration-300"
              onClick={() => handleBlogClick(post)}
            >
              <img
                src={post.image}
                alt={post.title}
                className="w-full h-[200px] sm:h-[303px] object-cover rounded-[20px]"
                loading="lazy"
              />
              <div className="p-4">
                <h3 className="text-black font-light text-sm md:text-base line-clamp-2 mb-1">
                  {post.title}
                </h3>
                <p className="text-sm text-black font-extralight line-clamp-2">
                  {post.description}
                </p>
                <div className="text-xs text-black font-extralight mt-2">
                  <span className="mr-2">{post.category}</span> |{" "}
                  <span className="ml-2">{post.readTime}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="col-span-full text-center py-8 text-black">
          No blogs found.
        </div>
      )}

      {/* Pagination */}
      <div className="">
        <div className="flex flex-col justify-center items-center -mb-[30px] md:-mb-[50px] pt-[50px]">
          <img src={PaginationBorder} alt="" />
        </div>
        <div className="flex flex-row items-center justify-around mt-10 gap-4 ">
          <button
            className="text-sm text-[#FF7731] "
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            ◀ Previous
          </button>
          <div className="flex flex-wrap gap-2 justify-center ">
            {renderPageNumbers().map((num, i) =>
              num === "..." ? (
                <span
                  key={i}
                  className="w-8 h-8 flex items-center justify-center text-[#FF7731]"
                >
                  ...
                </span>
              ) : (
                <button
                  key={i}
                  className={`w-8 h-8 text-sm rounded-md   flex items-center justify-center ${
                    num === currentPage
                      ? "bg-[#FF7731] text-white"
                      : "bg-white text-[#FF7731]"
                  }`}
                  onClick={() => handlePageChange(num)}
                >
                  {num}
                </button>
              )
            )}
          </div>

          <button
            className="text-sm text-[#FF7731]"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Next ▶
          </button>
        </div>
      </div>
    </section>
  );
}

export default ExploreMore;
