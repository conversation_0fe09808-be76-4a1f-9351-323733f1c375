import React, { useState, useEffect } from "react";
import logo from "/assets/Logo.svg";
import { useNavigate } from "react-router-dom";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faBars,
  faTimes,
  faAngleDown,
} from "@fortawesome/free-solid-svg-icons";

function Navbar() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [menuOpen, setMenuOpen] = useState(false);
  const [lastScrollTop, setLastScrollTop] = useState(0);
  const toggleMenu = () => setMenuOpen(!menuOpen);
  const navigate = useNavigate();

  // Hook to track scroll position and direction
  useEffect(() => {
    const handleScroll = () => {
      // Check for both window scroll and custom scroll container
      const scrollContainer = document.querySelector(".overflow-y-scroll");
      const currentScrollPos = scrollContainer
        ? scrollContainer.scrollTop
        : window.scrollY;

      // Determine if we're scrolled down enough for shadow
      setIsScrolled(currentScrollPos > 50);

      // Show/hide based on scroll direction
      const isScrollingDown = currentScrollPos > lastScrollTop;

      setIsVisible(
        currentScrollPos < 50 || // Always visible at top
          !isScrollingDown // Visible when scrolling up
      );

      setLastScrollTop(currentScrollPos);
    };

    // Listen to both window and custom scroll events
    window.addEventListener("scroll", handleScroll);

    // Add custom scroll event listener for custom scrollbar
    const scrollContainer = document.querySelector(".overflow-y-scroll");
    if (scrollContainer) {
      scrollContainer.addEventListener("scroll", handleScroll);
    }

    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (scrollContainer) {
        scrollContainer.removeEventListener("scroll", handleScroll);
      }
    };
  }, [lastScrollTop]);

  // Disable scrolling when the menu is open
  useEffect(() => {
    document.body.style.overflow = menuOpen ? "hidden" : "auto";
    return () => {
      document.body.style.overflow = "auto";
    };
  }, [menuOpen]);

  // Automatically close the menu when the screen width is lg or larger
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setMenuOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial check

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // Smooth scroll handler
  const handleSmoothScroll = (e, targetId) => {
    e.preventDefault();
    const targetElement = document.getElementById(targetId);

    if (targetElement) {
      // Sections that should not have an offset
      const noOffsetSections = ["work-story", "contact-us"];

      // Determine the offset based on the target section
      const offset = noOffsetSections.includes(targetId) ? 0 : 70;

      // Find the scrollable container
      const scrollContainer = document.querySelector(".overflow-y-scroll");

      if (scrollContainer) {
        scrollContainer.scrollTo({
          top: targetElement.offsetTop - offset,
          behavior: "smooth",
        });
      } else {
        // Fallback to window scroll
        window.scrollTo({
          top: targetElement.offsetTop - offset,
          behavior: "smooth",
        });
      }
    }
  };

  return (
    <nav>
      <div
        className={`fixed top-0 left-0 right-0 font-metrophobic flex justify-between items-center bg-[#F9F8F2] w-full h-[70px] px-6 z-[80] transition-all duration-300 ${
          isScrolled ? "shadow-lg" : ""
        } ${isVisible ? "translate-y-0" : "-translate-y-full"}`}
      >
        {/* Logo */}
        <img src={logo} alt="Logo" className="w-36 xl:w-40" />

        {/* Desktop Links */}
        <div className="hidden lg:flex justify-center items-center gap-10">
          <ul className="flex gap-7 xl:gap-16 text-[#333237] font-metrophobic font-normal text-base">
            <li className="link cursor-pointer" onClick={() => navigate("/")}>
              <p>Home</p>
            </li>
            <li className="link cursor-pointer">
              <a
                href="#work-story"
                onClick={(e) => handleSmoothScroll(e, "work-story")}
              >
                Work Story
              </a>
            </li>
            <li
              className="flex items-center link cursor-pointer"
              onClick={(e) => handleSmoothScroll(e, "success-section")}
            >
              <a href="#" className="flex items-center gap-2">
                Company
                {/* <FontAwesomeIcon icon={faAngleDown} /> */}
              </a>
            </li>
            <li
              className="flex items-center link cursor-pointer"
              onClick={(e) => handleSmoothScroll(e, "service")}
            >
              <a href="#service" className="flex items-center gap-2">
                Services
                {/* <FontAwesomeIcon icon={faAngleDown} /> */}
              </a>
            </li>
            <li
              className="link cursor-pointer"
              onClick={() => navigate("/blog")}
            >
              <p>Blog</p>
            </li>

            <li
              className="link cursor-pointer"
              onClick={(e) => handleSmoothScroll(e, "contact-us")}
            >
              <a href="#contact-us">Contact Us</a>
            </li>
          </ul>

          {/* Button */}
          <button className="bg-[#0E202B] px-5 py-2 md:px-8 md:py-3 xl:px-8 xl:py-3 rounded-[33px]">
            <a
              target="_blank"
              href="https://calendly.com/flexioninfotech/30min"
              className="text-[#FFFFFF] font-normal text-base"
            >
              Book a Meeting
            </a>
          </button>
        </div>

        {/* Hamburger Menu Icon */}
        <div className="lg:hidden flex items-center z-[9999]">
          <button onClick={toggleMenu}>
            <FontAwesomeIcon
              icon={menuOpen ? faTimes : faBars}
              className="text-[#0E202B] h-8 w-8"
            />
          </button>
        </div>
      </div>

      {/* Overlay when the menu is open */}
      {menuOpen && (
        <div
          className="fixed top-0 left-0 w-full h-full bg-black opacity-50 z-40"
          onClick={toggleMenu}
        ></div>
      )}

      {/* Mobile Menu */}
      <div
        className={`fixed top-0 right-0 w-64 h-full bg-[#F9F8F2] shadow-lg transition-transform duration-500 transform ${
          menuOpen ? "translate-x-0" : "translate-x-full"
        } z-[98] rounded-tl-lg rounded-bl-lg border-l border-[#FFFFFF]`}
      >
        {/* Close Button Inside Mobile Menu */}
        <div className="flex justify-end p-4">
          <button onClick={toggleMenu} className="text-[#4037A0]">
            <FontAwesomeIcon
              icon={faTimes}
              className="text-[#4B4B4B] h-8 w-8"
            />
          </button>
        </div>

        <ul className="flex flex-col items-center mt-16 gap-6">
          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={() => {
              toggleMenu();
              navigate("/");
            }}
          >
            <p>Home</p>
          </li>
          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={(e) => {
              toggleMenu();
              handleSmoothScroll(e, "work-story");
            }}
          >
            <p>Work Story</p>
          </li>
          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={(e) => {
              toggleMenu();
              handleSmoothScroll(e, "success-section");
            }}
          >
            <p>Company</p>
          </li>
          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={(e) => {
              toggleMenu();
              handleSmoothScroll(e, "service");
            }}
          >
            <p>Services</p>
          </li>
          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={() => {
              toggleMenu();
              navigate("/blog");
            }}
          >
            <p>Blog</p>
          </li>

          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={(e) => {
              toggleMenu();
              handleSmoothScroll(e, "contact-us");
            }}
          >
            <p>Contact Us</p>
          </li>

          <li
            className="font-medium text-[#333237] hover:text-[#F0EDFF] hover:bg-[#4B4B4B] px-8 py-2 rounded-2xl transform hover:scale-110 duration-500 cursor-pointer"
            onClick={() => {
              toggleMenu();
            }}
          >
            <a
              target="_blank"
              href="https://calendly.com/flexioninfotech/30min"
            >
              Book a Meeting
            </a>
          </li>
        </ul>
      </div>
    </nav>
  );
}

export default Navbar;
