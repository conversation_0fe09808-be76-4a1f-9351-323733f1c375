import "./App.css";
import "./index.css";
import Navbar from "./components/Navbar/Navbar";
import Hero from "./components/Hero/Hero";
import { BrowserRouter as Router, Routes, Route } from "react-router-dom";

import Work from "./components/Work/Work";
import Cards from "./components/Cards/Cards";
import Success from "./components/Success/Success";
import Services from "./components/ourServices/Services";
import Testimonials from "./components/Testimonials/Testimonials";
import Blogs from "./components/Blogs/Blogs";
import Blogs_page from "./components/Blogs/Blogs/index";
import SingleBlogView from "./components/Blogs/Blogs/SingleBlogView";
import ContactForm from "./components/Form/Form";
import Footer from "./components/Footer/Footer";
import { MuteProvider } from "./components/Work/MuteContext";
import Scrollbar from "./components/Navbar/Scrollbar";

function HomePage() {
  return (
    <>
      <Navbar />
      <Hero />
      <Work />
      <Cards />
      <Success />
      <Services />
      {/* <Testimonials /> */}
      <Blogs />
      <ContactForm />
      <Footer />
    </>
  );
}

function App() {
  return (
    <Scrollbar>
      <MuteProvider>
        <Router>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route
              path="/blog"
              element={
                <>
                  <Navbar />
                  <Blogs_page />
                  <Footer />
                </>
              }
            />
            <Route
              path="/blog/:slug"
              element={
                <>
                  <Navbar />
                  <SingleBlogView />
                  <Footer />
                </>
              }
            />
          </Routes>
        </Router>
      </MuteProvider>
    </Scrollbar>
  );
}

export default App;
